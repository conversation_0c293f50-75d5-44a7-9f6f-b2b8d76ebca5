<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PlanningPresetCategoryMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.PlanningPresetCategory" id="PlanningPresetCategoryMap">
        <!--@mbg.generated-->
        <!--@Table planning_preset_category-->
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="platformId" column="platform_id" jdbcType="BIGINT"/>
        <result property="supplyMethodCode" column="supply_method_code" jdbcType="VARCHAR"/>
        <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
        <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
        ,platform_id
        ,supply_method_code
        ,category_id
        ,category_code
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>
</mapper>

