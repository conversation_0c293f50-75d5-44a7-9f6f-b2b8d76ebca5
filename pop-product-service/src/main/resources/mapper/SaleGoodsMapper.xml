<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.SaleGoodsMapper">

    <!-- 多条件 IN 查询 -->
    <select id="selectByProductIdAndCountry" resultType="tech.tiangong.pop.dao.entity.SaleGoods">
        SELECT *
        FROM sale_goods
        WHERE product_id IN
        <foreach item="item" index="index" collection="productIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND country IN
        <foreach item="country" index="index" collection="countries" open="(" separator="," close=")">
            #{country}
        </foreach>
        AND deleted = 0
    </select>


    <!-- 查询属性信息 -->
    <select id="findPlatformAttributes" resultType="tech.tiangong.pop.dto.product.PublishPlatformAttributeDTO">
        SELECT ppla.platform_attr_id,
               ppa.platform_attribute_value,
               ppa.platform_attribute_code,
               ppla.platform_attribute_key_name,
               ppla.platform_attribute_label_name,
               attr.attribute_name,
               val.attribute_value
        FROM product_attributes pa
                 JOIN
             publish_attribute attr
             ON pa.attribute_id = attr.attribute_id
                 JOIN
             publish_attribute_value val
             ON val.attribute_value_id = pa.attribute_value_id
                 AND val.attribute_id = pa.attribute_id
                 JOIN
             publish_platform_attr ppla
             ON pa.category_id = ppla.category_id
                 AND pa.platform_id = ppla.platform_id
                 AND pa.attribute_id = ppla.attribute_id
                 JOIN
             publish_platform_attr_value ppa
             ON ppla.platform_attr_id = ppa.publish_platform_attr_id
                 AND pa.attribute_value_id = ppa.attribute_value_id
        WHERE pa.category_id = #{categoryId}
          AND pa.platform_id = #{platformId}
          AND pa.product_id = #{productId}
          AND ppla.platform_id = #{platformId}
          AND ppla.category_id = #{categoryId}
          AND pa.deleted = 0
          AND ppla.deleted = 0
          AND val.state = 1
          AND ppa.deleted = 0;
    </select>

    <!-- 获取每个产品最早上架的记录 -->
    <select id="findFirstPublishedGoods" resultType="tech.tiangong.pop.dao.entity.SaleGoods">
        SELECT sg.*
        FROM (
            SELECT sg1.*,
            ROW_NUMBER() OVER (PARTITION BY sg1.product_id ORDER BY sg1.publish_time ASC, sg1.sale_goods_id ASC) as rn
            FROM sale_goods sg1
            WHERE sg1.shop_id IN
            <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
            AND sg1.publish_time BETWEEN #{publishDateStart} AND #{publishDateEnd}
            AND sg1.deleted = 0
            AND sg1.publish_state IN (1, 2)
            AND sg1.platform_product_id IS NOT NULL
        ) AS sg
        WHERE sg.rn = 1
        ORDER BY sg.publish_time ASC
    </select>

    <select id="findPublishProductGeneralTemplate" resultType="tech.tiangong.pop.dto.product.PublishProductGeneralTemplateDTO">
        SELECT
        sg.platform_product_id,
        sg.shop_name,
        shop.country_type as shopCountryType,
        sg.country,
        sg.spu_code AS spu,
        p.supply_mode,
        p.waves,
        p.goods_type,
        sg.publish_time,
        <!--MAX(psk.cost_price) AS maxCostPrice,-->
        MAX(psk.local_price) AS maxLocalPrice,
        MAX(psk.cb_price) AS maxCbPrice,
        MAX(psk.purchase_price) AS maxPurchasePrice,
        MAX(ss.retail_price) AS retailPrice,
        MAX(ss.sale_price) AS salePrice,
        p.main_img_url AS mainImgUrl,
        p.category_name AS categoryName
        FROM
        product p
        JOIN sale_goods sg ON p.product_id = sg.product_id
        LEFT JOIN sale_sku ss ON sg.sale_goods_id = ss.sale_goods_id
        LEFT JOIN product_skc psk ON ss.product_skc_id = psk.product_skc_id
        LEFT JOIN shop shop ON shop.shop_id = sg.shop_id
        WHERE
        sg.platform_product_id IS NOT NULL
        AND sg.publish_state IN (1, 2)
        AND sg.deleted = 0
        AND p.deleted = 0
        AND ss.deleted = 0
        AND sg.shop_id IN
        <foreach collection="req.lazadaShopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        AND sg.publish_time BETWEEN #{req.publishDateStart} AND #{req.publishDateEnd}
        GROUP BY
        sg.platform_product_id,
        sg.shop_name,
        sg.country,
        sg.spu_code,
        p.supply_mode,
        p.goods_type,
        sg.publish_time,
        p.main_img_url
        ORDER BY
        sg.publish_time, sg.spu_code DESC
    </select>

    <select id="pageLazada" resultType="tech.tiangong.pop.resp.product.lazada.ProductLazadaPageResp">
        SELECT
            p.product_id,
            p.main_img_url,
            p.spu_code,
            p.supply_mode,
            p.waves,
            p.category_code,
            p.category_name,
            p.creator_id,
            p.creator_name,
            p.created_time,
            p.reviser_id,
            p.reviser_name,
            p.revised_time,
            p.price_exception,
            p.is_sync_platform as syncState,
            p.image_package_state,
            p.style_type,
            p.market_code,
            p.market_series_code,
            p.clothing_style_code,
            p.planning_type,
            p.select_style_name,
            sg.update_state AS `update`,
            sg.cost_price_update_state,
            sg.platform_id,
            sg.channel_id,
            sg.error_state as is_error,
            sg.error_info,
            sg.sale_goods_id,
            sg.shop_id,
            sg.shop_name,
            sg.platform_product_id,
            GROUP_CONCAT(ps.skc) as skc_list
        FROM
            product p
            JOIN sale_goods sg ON p.product_id = sg.product_id
            JOIN sale_skc ps ON sg.sale_goods_id = ps.sale_goods_id
        WHERE
        p.deleted = 0
        AND sg.deleted = 0
        AND sg.shop_id is NOT NULL
        <if test="req.supplyMode != null and req.supplyMode != ''">
            AND p.supply_mode = #{req.supplyMode}
        </if>
        <if test="req.selectStyleName != null and req.selectStyleName != ''">
            AND p.select_style_name  LIKE CONCAT('%', #{req.selectStyleName}, '%')
        </if>
        <if test="req.updateFlag != null and req.updateFlag == 1">
            AND sg.update_state = #{req.updateFlag}
        </if>
        <if test="req.costPriceUpdateState != null">
            AND sg.cost_price_update_state = #{req.costPriceUpdateState}
        </if>
        <if test="req.spuList != null and req.spuList.size > 1">
            AND p.spu_code IN
            <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.spuList != null and req.spuList.size == 1">
            AND p.spu_code
            <foreach item="item" index="index" collection="req.spuList">
                LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="req.publishState != null and req.publishState != ''">
            AND sg.publish_state = #{req.publishState}
        </if>
        <if test="req.clothingStyleCode != null and req.clothingStyleCode != ''">
            AND p.clothing_style_code = #{req.clothingStyleCode}
        </if>
        <if test="req.planningType != null ">
            AND p.planning_type = #{req.planningType}
        </if>
        <if test="req.marketCode != null and req.marketCode != ''">
            AND p.market_code = #{req.marketCode}
        </if>
        <if test="req.marketSeriesCode != null and req.marketSeriesCode != ''">
            AND p.market_series_code = #{req.marketSeriesCode}
        </if>
        <if test="req.waves != null and req.waves != ''">
            AND p.waves = #{req.waves}
        </if>
        <if test="req.productTitle != null and req.productTitle != ''">
            AND sg.product_title LIKE CONCAT('%', #{req.productTitle}, '%')
        </if>
        <if test="req.shopId != null">
            AND sg.shop_id = #{req.shopId}
        </if>
        <if test="req.productId != null">
            AND p.product_id = #{req.productId}
        </if>
        <if test="req.shopName != null and req.shopName != ''">
            AND sg.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
        </if>
        <if test="req.country != null and req.country != ''">
            AND sg.country = #{req.country}
        </if>
        <if test="req.publishState != null">
            AND sg.publish_state = #{req.publishState}
        </if>
        <if test="req.createdTimeStart != null and req.createdTimeEnd != null">
            AND sg.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
        </if>
        <if test="req.publishTimeStart != null and req.publishTimeEnd != null">
            AND sg.publish_time BETWEEN #{req.publishTimeStart} AND #{req.publishTimeEnd}
        </if>
        <if test="req.creatorName != null and req.creatorName != ''">
            AND sg.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
        </if>
        <if test="req.reviserName != null and req.reviserName != ''">
            AND sg.reviser_name LIKE CONCAT('%', #{req.reviserName}, '%')
        </if>
        <if test="req.publishAuthor != null and req.publishAuthor != ''">
            AND sg.publish_user_name LIKE CONCAT('%', #{req.publishAuthor}, '%')
        </if>
        <if test="req.stockTypes != null and req.stockTypes.size > 0">
            AND sg.stock_type IN
            <foreach item="item" index="index" collection="req.stockTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.delayDeliveryDays != null">
            AND sg.delay_delivery_days = #{req.delayDeliveryDays}
        </if>
        <if test="req.reviserTimeStart != null  and req.reviserTimeEnd != null ">
            AND  sg.revised_time &gt;= #{req.reviserTimeStart}  AND sg.revised_time &lt;= #{req.reviserTimeEnd}
        </if>
        <if test="req.categoryCode != null and req.categoryCode != ''">
            AND sg.category_code = #{req.categoryCode}
        </if>
        <if test="req.skcList != null and req.skcList.size > 0">
            AND ps.skc IN
            <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.errorFlag != null">
            AND sg.error_state = #{req.errorFlag}
        </if>
        
        <!-- 指定查询的sale_goods_id列表 -->
        <if test="req.saleGoodsIds != null and req.saleGoodsIds.size() > 0">
            AND sg.sale_goods_id IN
            <foreach collection="req.saleGoodsIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        
        <if test="req.tagCodes != null and req.tagCodes.size > 0">
            AND
            (
                sg.sale_goods_id in (select target_id from product_tag where deleted = 0 and
                <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                    <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                    )
                </foreach>
                )
                OR
                ps.sale_skc_id in (select target_id from product_tag where deleted = 0 and
                    <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                        <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                            #{value}
                        </foreach>
                        )
                    </foreach>
                )
                OR
                p.product_id in (select target_id from product_tag where deleted = 0 and
                    <foreach collection="req.tagCodes" item="valueList" index="key" open="(" close=")" separator=" AND ">(tag_key = #{key} AND tag_value IN
                        <foreach collection=" valueList" item="value" open="(" close=")" separator=",">
                            #{value}
                        </foreach>
                        )
                    </foreach>
                )
            )
        </if>
        GROUP BY
        p.product_id, sg.shop_id
        ORDER BY
        sg.revised_time DESC
    </select>

    <update id="fixEtaUpdate">
        <foreach collection="list" item="data" index="index" separator=";">
        update sale_goods
        set
            <if test="data.stockType !=null">
                stock_type = #{data.stockType},
            </if>
            <if test="data.countryStockType !=null">
                country_stock_type = #{data.countryStockType},
            </if>
            <if test="data.delayDeliveryDays !=null">
                delay_delivery_days = #{data.delayDeliveryDays},
            </if>
            revised_time = now()
        where sale_goods_id = #{data.saleGoodsId}
        </foreach>
    </update>

    <!-- 获取所有需要同步的商品 -->
    <select id="pageAllSaleGoodsForSync" resultType="tech.tiangong.pop.dao.entity.SaleGoods">
        SELECT sg.*
        FROM sale_goods sg
        <where>
             <!-- platform_product_id 必须存在 -->
            sg.platform_product_id IS NOT NULL 
			AND sg.deleted = 0
            <!-- 游标条件 -->
            <if test="lastId != null">
                AND sg.sale_goods_id &gt; #{lastId}
            </if>

            <!-- 商品id列表 -->
            <if test="req.saleGoodsIds != null and req.saleGoodsIds.size() > 0">
                AND sg.sale_goods_id IN
                <foreach collection="req.saleGoodsIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 创建时间范围 -->
            <if test="req.createdTimeStart != null">
                AND sg.created_time &gt;= #{req.createdTimeStart}
            </if>
            <if test="req.createdTimeEnd != null">
                AND sg.created_time &lt;= #{req.createdTimeEnd}
            </if>

            <!-- 店铺ID批量 -->
            <if test="req.shopIds != null and req.shopIds.size() > 0">
                AND sg.shop_id IN
                <foreach collection="req.shopIds" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>

            <!-- 商品状态批量 -->
            <if test="req.publishStates != null and req.publishStates.size() > 0">
                AND sg.publish_state IN
                <foreach collection="req.publishStates" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>

            <!-- 最近定时任务执行时间范围 -->
            <if test="req.lastScheduledSyncTimeStart != null">
                AND sg.last_scheduled_sync_time &gt;= #{req.lastScheduledSyncTimeStart}
            </if>
            <if test="req.lastScheduledSyncTimeEnd != null">
                AND sg.last_scheduled_sync_time &lt;= #{req.lastScheduledSyncTimeEnd}
            </if>

            <!-- 商品不存在批量 -->
            <if test="req.productNotFounds != null and req.productNotFounds.size() > 0">
                AND sg.product_not_found IN
                <foreach collection="req.productNotFounds" item="pnf" open="(" separator="," close=")">
                    #{pnf}
                </foreach>
            </if>
        </where>
        ORDER BY sg.sale_goods_id LIMIT #{pageSize}
    </select>

    <!-- 忽略逻辑删除字段查询指定ID的记录 -->
    <select id="findByIdsIgnoreDeletedFilter" resultType="tech.tiangong.pop.dao.entity.SaleGoods">
        SELECT *
        FROM sale_goods 
        WHERE sale_goods_id IN 
        <foreach collection="saleGoodsIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    
    <!-- 全量同步查询（忽略逻辑删除过滤） -->
    <select id="findForFullSyncIgnoreDeleted" resultType="tech.tiangong.pop.dao.entity.SaleGoods">
        SELECT *
        FROM sale_goods 
        WHERE sale_goods_id > #{lastId}
        ORDER BY sale_goods_id
        LIMIT #{pageSize}
    </select>
</mapper>
