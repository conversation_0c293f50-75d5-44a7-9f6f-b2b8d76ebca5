<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ImageQcRecordMapper">
    <select id="pageQuery" resultType="tech.tiangong.pop.resp.image.ImageQcRecordPageVO">
        SELECT iqr.image_qc_record_id,
               iqr.main_image_url,
               iqr.spu_code,
               iqr.qc_result,
               iqr.problem_categories,
               iqr.problem_images,
               iqr.qc_uploaded_images,
               iqr.remark,
               iqr.repair_status,
               iqr.repair_complete_time,
               iqr.creator_name,
               iqr.created_time,
               iqr.image_repo_creator_id,
               iqr.image_repo_creator_name,
               iqr.image_repo_created_time,
                iqr.image_repo_reviser_id,
                iqr.image_repo_reviser_name,
                iqr.image_repo_revised_time
        FROM image_qc_record iqr
        WHERE iqr.deleted = 0
        <if test="query.spuCode != null and query.spuCode != ''">
            AND iqr.spu_code LIKE CONCAT('%', #{query.spuCode}, '%')
        </if>
        <if test="query.spuCodes != null and query.spuCodes.size() > 0">
            AND iqr.spu_code IN
            <foreach item="spuCode" collection="query.spuCodes" open="(" separator="," close=")">
                #{spuCode}
            </foreach>
        </if>
        <if test="query.qcResult != null">
            AND iqr.qc_result = #{query.qcResult}
        </if>
        <if test="query.repairStatus != null">
            AND iqr.repair_status = #{query.repairStatus}
        </if>
        <if test="query.qcStartTime != null">
            AND iqr.created_time <![CDATA[ >= ]]> #{query.qcStartTime}
        </if>
        <if test="query.qcEndTime != null">
            AND iqr.created_time <![CDATA[ <= ]]> #{query.qcEndTime}
        </if>
        <if test="query.repairStartTime != null">
            AND iqr.repair_complete_time <![CDATA[ >= ]]> #{query.repairStartTime}
        </if>
        <if test="query.repairEndTime != null">
            AND iqr.repair_complete_time <![CDATA[ <= ]]> #{query.repairEndTime}
        </if>
        <if test="query.creatorId != null">
            AND iqr.creator_id = #{query.creatorId}
        </if>
        <if test="query.creatorName != null and query.creatorName != ''">
            AND iqr.creator_name = #{query.creatorName}
        </if>
        <if test="query.imageRepoCreatorId != null">
            AND iqr.image_repo_creator_id = #{query.imageRepoCreatorId}
        </if>
        <if test="query.imageRepoCreatorName != null and query.imageRepoCreatorName != ''">
            AND iqr.image_repo_creator_name = #{query.imageRepoCreatorName}
        </if>
        <if test="query.imageRepoCreatedStartTime != null">
            AND iqr.image_repo_created_time <![CDATA[ >= ]]> #{query.imageRepoCreatedStartTime}
        </if>
        <if test="query.imageRepoCreatedEndTime != null">
            AND iqr.image_repo_created_time <![CDATA[ <= ]]> #{query.imageRepoCreatedEndTime}
        </if>
        <if test="query.imageRepoReviserId != null">
            AND iqr.image_repo_reviser_id = #{query.imageRepoReviserId}
        </if>
        <if test="query.imageRepoReviserName != null and query.imageRepoReviserName != ''">
            AND iqr.image_repo_reviser_name = #{query.imageRepoReviserName}
        </if>
        <if test="query.imageRepoRevisedStartTime != null">
            AND iqr.image_repo_revised_time <![CDATA[ >= ]]> #{query.imageRepoRevisedStartTime}
        </if>
        <if test="query.imageRepoRevisedEndTime != null">
            AND iqr.image_repo_revised_time <![CDATA[ <= ]]> #{query.imageRepoRevisedEndTime}
        </if>
        ORDER BY iqr.created_time DESC
    </select>
</mapper>
