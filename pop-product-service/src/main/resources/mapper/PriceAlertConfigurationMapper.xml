<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PriceAlertConfigurationMapper">
    <select id="pageQueryWithDetail" resultType="tech.tiangong.pop.resp.settings.PriceAlertConfigurationResp">
        SELECT
            pac.price_alert_configuration_id,
            pac.platform_id,
            pac.publish_category_id,
            pac.publish_category_code,
            pac.publish_category_path,
            pac.cost_price_min,
            pac.cost_price_max,
            pac.cost_currency_type,
            pac.creator_id,
            pac.creator_name,
            pac.created_time,
            pac.reviser_id,
            pac.reviser_name,
            pac.revised_time,
            -- 新增字段
            pcm.platform_category_id,
            pcm.platform_category_name
        FROM
            price_alert_configuration pac
        LEFT JOIN
        (SELECT
            platform_id,
            publish_category_id,
            platform_category_id,
            platform_category_name,
            ROW_NUMBER() OVER(PARTITION BY platform_id, publish_category_id ORDER BY revised_time DESC) as rn
            FROM publish_category_mapping
            WHERE deleted = 0
        ) pcm ON pac.platform_id = pcm.platform_id
            AND pac.publish_category_id = pcm.publish_category_id
            AND pcm.rn = 1
        WHERE
            pac.deleted = 0
            <if test="req.platform != null">
                AND pac.platform_id = #{req.platform.platformId}
            </if>
            <if test="req.publishCategoryPath != null and req.publishCategoryPath != ''">
                AND pac.publish_category_path LIKE CONCAT('%', #{req.publishCategoryPath}, '%')
            </if>
        ORDER BY
        pac.revised_time DESC
    </select>
</mapper>