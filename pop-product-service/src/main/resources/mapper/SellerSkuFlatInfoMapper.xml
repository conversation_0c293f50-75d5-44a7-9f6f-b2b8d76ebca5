<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.SellerSkuFlatInfoMapper">

    <select id="pageByComboBarcodePageReq" resultType="tech.tiangong.pop.dao.entity.SellerSkuFlatInfo">
        select a.*
        from seller_sku_flat_info a
            join seller_sku_barcode_ref b on a.seller_sku_flat_id = b.seller_sku_flat_id
        <where>
            a.combo = 1
            <if test="req.spuList!= null and req.spuList.size > 0">
                and a.spu_code in
                <foreach collection="req.spuList" item="spuCode" open="(" separator="," close=")">
                    #{spuCode}
                </foreach>
            </if>
            <if test="req.barcodeList != null and req.barcodeList.size > 0">
                and b.barcode in
                <foreach collection="req.barcodeList" item="barcode" open="(" separator="," close=")">
                    #{barcode}
                </foreach>
            </if>
            <if test="req.sellerSkuList!= null and req.sellerSkuList.size > 0">
                and a.seller_sku in
                <foreach collection="req.sellerSkuList" item="sellerSku" open="(" separator="," close=")">
                    #{sellerSku}
                </foreach>
            </if>
            <if test="req.platformId!= null">
                and a.platform_id = #{req.platformId}
            </if>
            <if test="req.shopId!= null">
                and a.shop_id = #{req.shopId}
            </if>
        </where>
        group by a.seller_sku_flat_id
        order by a.seller_sku_flat_id desc
    </select>
</mapper>

