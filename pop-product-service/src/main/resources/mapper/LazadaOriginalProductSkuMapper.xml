<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.LazadaOriginalProductSkuMapper">

    <select id="selectAllByIds" resultType="tech.tiangong.pop.dao.entity.LazadaOriginalProductSku">
        select *
        from lazada_original_product_sku
        where sku_id IN
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="updateDeletedByIds">
        update lazada_original_product_sku
        set deleted = #{deleted}
        where sku_id IN
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>

