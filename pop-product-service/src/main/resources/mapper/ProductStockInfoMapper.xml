<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductStockInfoMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.ProductStockInfo" id="ProductStockInfoMap">
        <!--@mbg.generated-->
        <!--@Table product_stock_info-->
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="spuCode" column="spu_code" jdbcType="VARCHAR"/>
        <result property="skcCode" column="skc_code" jdbcType="VARCHAR"/>
        <result property="sizeGroup" column="size_group" jdbcType="VARCHAR"/>
        <result property="size" column="size" jdbcType="VARCHAR"/>
        <result property="color" column="color" jdbcType="VARCHAR"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="stockQuantity" column="stock_quantity" jdbcType="INTEGER"/>
        <result property="onWayStockQuantity" column="on_way_stock_quantity" jdbcType="INTEGER"/>
        <result property="usableStockQuantity" column="usable_stock_quantity" jdbcType="INTEGER"/>
        <result property="occupyStockQuantity" column="occupy_stock_quantity" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
        ,spu_code
        ,skc_code
        ,size_group
        ,size
        ,color
        ,barcode
        ,stock_quantity
        ,on_way_stock_quantity
        ,usable_stock_quantity
        ,occupy_stock_quantity
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>

    <select id="tagAvailableInvCompute" resultType="tech.tiangong.pop.dao.entity.dto.TagAvailableInvComputeDto">
        select spu_code,
               if(total_stock > 0, 1, 0) as tag_available_inv
        from (select spu_code, sum(stock_quantity) as total_stock
              from product_stock_info
              where spu_code in
                <foreach collection="spuCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and deleted = 0
              group by spu_code) t
    </select>
</mapper>

