<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PublishCategoryMappingMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.PublishCategoryMapping" id="PublishCategoryMappingMap">
        <!--@mbg.generated-->
        <!--@Table publish_category_mapping-->
        <id property="categoryMappingId" column="category_mapping_id" jdbcType="BIGINT"/>
        <result property="publishCategoryId" column="publish_category_id" jdbcType="BIGINT"/>
        <result property="platformId" column="platform_id" jdbcType="BIGINT"/>
        <result property="platformCategoryId" column="platform_category_id" jdbcType="VARCHAR"/>
        <result property="platformCategoryName" column="platform_category_name" jdbcType="VARCHAR"/>
        <result property="country" column="country" jdbcType="VARCHAR"/>
        <result property="channelName" column="channel_name" jdbcType="VARCHAR"/>
        <result property="platformName" column="platform_name" jdbcType="VARCHAR"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        category_mapping_id
        ,publish_category_id
        ,platform_id
        ,platform_category_id
        ,platform_category_name
        ,country
        ,channel_name
        ,platform_name
        ,channel_id
        ,remark
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>

    <select id="pageQuery" resultType="tech.tiangong.pop.resp.category.PublishCategoryMappingVo">
        SELECT category_mapping_id,
               publish_category_id,
               platform_id,
               platform_category_id,
               platform_category_name,
               country,
               channel_name,
               platform_name,
               channel_id,
               remark,
               reviser_name,
               revised_time
        FROM publish_category_mapping
        WHERE publish_category_id = #{query.publishCategoryId}
          AND deleted = 0
        ORDER BY revised_time DESC
    </select>
</mapper>

