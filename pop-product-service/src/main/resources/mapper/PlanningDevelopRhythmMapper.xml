<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PlanningDevelopRhythmMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.PlanningDevelopRhythm" id="PlanningDevelopRhythmMap">
        <!--@mbg.generated-->
        <!--@Table planning_develop_rhythm-->
        <id property="developRhythmId" column="develop_rhythm_id" jdbcType="BIGINT"/>
        <result property="planningId" column="planning_id" jdbcType="BIGINT"/>
        <result property="shopId" column="shop_id" jdbcType="BIGINT"/>
        <result property="shopName" column="shop_name" jdbcType="VARCHAR"/>
        <result property="countrySiteCode" column="country_site_code" jdbcType="VARCHAR"/>
        <result property="countrySiteName" column="country_site_name" jdbcType="VARCHAR"/>
        <result property="supplyModeCode" column="supply_mode_code" jdbcType="VARCHAR"/>
        <result property="supplyModeName" column="supply_mode_name" jdbcType="VARCHAR"/>
        <result property="publishDate" column="publish_date" jdbcType="TIMESTAMP"/>
        <result property="waveBatchCode" column="wave_batch_code" jdbcType="VARCHAR"/>
        <result property="supplyQuantity" column="supply_quantity" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        develop_rhythm_id
        ,planning_id
        ,shop_id
        ,shop_name
        ,country_site_code
        ,country_site_name
        ,supply_mode_code
        ,supply_mode_name
        ,publish_date
        ,wave_batch_code
        ,supply_quantity
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>

    <delete id="deleteByPlanningIdPhysical">
        delete from planning_develop_rhythm where planning_id = #{planningId}
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
        <if test="countrySiteCode != null and countrySiteCode != ''">
            and country_site_code = #{countrySiteCode}
        </if>
    </delete>
</mapper>

