<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.AeOriginalProductSkuMapper">


    <select id="selectAllBySkuIsd" resultType="tech.tiangong.pop.dao.entity.AeOriginalProductSku">
        select *
        from ae_original_product_sku
        where sku_id IN
        <foreach item="id" index="index" collection="skuIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="updateDeletedBySkuIds">
        update ae_original_product_sku
        set deleted = #{deleted}
        where sku_id IN
        <foreach item="id" index="index" collection="skuIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>

