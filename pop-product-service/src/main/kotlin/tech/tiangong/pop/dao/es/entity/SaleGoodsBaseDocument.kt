package tech.tiangong.pop.dao.es.entity

import cn.hutool.core.date.LocalDateTimeUtil
import org.dromara.easyes.annotation.IndexField
import org.dromara.easyes.annotation.rely.FieldType
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.resp.product.lazada.ProductLazadaPageResp

open class SaleGoodsBaseDocument {

    @IndexField(fieldType = FieldType.LONG)
    var productId: Long? = null

    @IndexField(exist = false)
    var mainImgUrl: String? = null

    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    var productTitle: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var supplyMode: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var waves: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var spuCode: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var tagCodes: List<String>? = null

    @IndexField(fieldType = FieldType.LONG)
    var channelId: Long? = null

    @IndexField(fieldType = FieldType.LONG)
    var platformId: Long? = null

    @IndexField(fieldType = FieldType.LONG)
    var shopId: Long? = null

    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    var shopName: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var country: String? = null

    @IndexField(fieldType = FieldType.LONG)
    var categoryId: Long? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var categoryCode: String? = null

    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    var categoryName: String? = null

    @IndexField(exist = false)
    var salePrice: String? = null

    @IndexField(fieldType = FieldType.LONG)
    var stockQuantity: Long? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var publishState: Int? = null

    @IndexField(exist = false)
    var frontUrlList: String? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var priceException: Int? = null

    @IndexField(fieldType = FieldType.BOOLEAN)
    var update: Boolean? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var costPriceUpdateState: Int? = null

    @IndexField(fieldType = FieldType.LONG)
    var creatorId: Long? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var creatorName: String? = null

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    var createdTime: String? = null

    @IndexField(fieldType = FieldType.LONG)
    var reviserId: Long? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var reviserName: String? = null

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    var revisedTime: String? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var isError: Int? = null

    @IndexField(exist = false)
    var errorInfoDtoList: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var publishUserName: String? = null

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    var firstPublishTime: String? = null

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    var latestPublishTime: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var latestPublishUserName: String? = null

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis")
    var latestOfflineTime: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var latestOfflineUserName: String? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var syncState: Int? = null

    @IndexField(exist = false)
    var errorMsg: String? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var stockType: Int? = null

    @IndexField(exist = false)
    var stockTypeName: String? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var delayDeliveryDays: Int? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var styleType: Int? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var imagePackageState: Int? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var marketCode: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var marketSeriesCode: String? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var clothingStyleCode: String? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var planningType: Int? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var selectStyleName: String? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var skuCount: Int? = null

    @IndexField(fieldType = FieldType.INTEGER)
    var skcCount: Int? = null

    @IndexField(fieldType = FieldType.LONG)
    var platformProductId: Long? = null

    @IndexField(fieldType = FieldType.KEYWORD)
    var skcList: List<String>? = null

    constructor()
    constructor(page: ProductLazadaPageResp) {
        this.productId = page.productId
        this.mainImgUrl = page.mainImgUrl
        this.productTitle = page.productTitle
        this.supplyMode = page.supplyMode
        this.waves = page.waves
        this.spuCode = page.spuCode
        this.tagCodes = page.tagCodes
        this.channelId = page.channelId
        this.platformId = page.platformId
        this.shopId = page.shopId
        this.shopName = page.shopName
        this.country = page.country
        this.categoryId = page.categoryId
        this.categoryCode = page.categoryCode
        this.categoryName = page.categoryName
        this.salePrice = page.salePrice
        this.stockQuantity = page.stockQuantity
        this.publishState = page.publishState
        this.frontUrlList = page.frontUrlList?.toJson()
        this.priceException = page.priceException
        this.update = page.update
        this.costPriceUpdateState = page.costPriceUpdateState
        this.creatorId = page.creatorId
        this.creatorName = page.creatorName
        this.createdTime = page.createdTime?.let(LocalDateTimeUtil::formatNormal)
        this.reviserId = page.reviserId
        this.reviserName = page.reviserName
        this.revisedTime = page.revisedTime?.let(LocalDateTimeUtil::formatNormal)
        this.isError = page.isError
        this.errorInfoDtoList = page.errorInfoDtoList?.toJson()
        this.publishUserName = page.publishUserName
        this.firstPublishTime = page.firstPublishTime?.let(LocalDateTimeUtil::formatNormal)
        this.latestPublishTime = page.latestPublishTime?.let(LocalDateTimeUtil::formatNormal)
        this.latestPublishUserName = page.latestPublishUserName
        this.latestOfflineTime = page.latestOfflineTime?.let(LocalDateTimeUtil::formatNormal)
        this.latestOfflineUserName = page.latestOfflineUserName
        this.syncState = page.syncState
        this.errorMsg = page.errorMsg
        this.stockType = page.stockType
        this.stockTypeName = page.stockTypeName
        this.delayDeliveryDays = page.delayDeliveryDays
        this.styleType = page.styleType
        this.imagePackageState = page.imagePackageState
        this.marketCode = page.marketCode
        this.marketSeriesCode = page.marketSeriesCode
        this.clothingStyleCode = page.clothingStyleCode
        this.planningType = page.planningType
        this.selectStyleName = page.selectStyleName
        this.skuCount = page.skuCount
        this.skcCount = page.skcCount
        this.platformProductId = page.platformProductId
        this.skcList = page.skcList?.split(",")
    }
    
    fun toDbPage(): ProductLazadaPageResp {
        return ProductLazadaPageResp().apply {
            productId = <EMAIL>
            mainImgUrl = <EMAIL>
            productTitle = <EMAIL>
            supplyMode = <EMAIL>
            waves = <EMAIL>
            spuCode = <EMAIL>
            tagCodes = <EMAIL>
            channelId = <EMAIL>
            platformId = <EMAIL>
            shopId = <EMAIL>
            shopName = <EMAIL>
            country = <EMAIL>
            categoryId = <EMAIL>
            categoryCode = <EMAIL>
            categoryName = <EMAIL>
            salePrice = <EMAIL>
            stockQuantity = <EMAIL>
            publishState = <EMAIL>

            // 解析前台链接JSON字符串为对象列表
            frontUrlList = <EMAIL>?.let { frontUrlJson ->
                try {
                    frontUrlJson.parseJsonList(tech.tiangong.pop.resp.product.ProductFrontUrlResp::class.java)
                } catch (e: Exception) {
                    null
                }
            }

            priceException = <EMAIL>
            update = <EMAIL> ?: false
            costPriceUpdateState = <EMAIL>
            creatorId = <EMAIL>
            creatorName = <EMAIL>

            // 解析时间字符串为LocalDateTime对象
            createdTime = <EMAIL>?.let { timeStr ->
                try {
                    LocalDateTimeUtil.parseNormal(timeStr)
                } catch (e: Exception) {
                    null
                }
            }

            reviserId = <EMAIL>
            reviserName = <EMAIL>

            revisedTime = <EMAIL>?.let { timeStr ->
                try {
                    LocalDateTimeUtil.parseNormal(timeStr)
                } catch (e: Exception) {
                    null
                }
            }

            isError = <EMAIL>

            // 解析错误信息JSON字符串为对象列表
            errorInfoDtoList = <EMAIL>?.let { errorJson ->
                try {
                    errorJson.parseJsonList(tech.tiangong.pop.dto.product.ProductErrorInfoDto::class.java)
                } catch (e: Exception) {
                    null
                }
            }

            publishUserName = <EMAIL>

            firstPublishTime = <EMAIL>?.let { timeStr ->
                try {
                    LocalDateTimeUtil.parseNormal(timeStr)
                } catch (e: Exception) {
                    null
                }
            }

            latestPublishTime = <EMAIL>?.let { timeStr ->
                try {
                    LocalDateTimeUtil.parseNormal(timeStr)
                } catch (e: Exception) {
                    null
                }
            }

            latestPublishUserName = <EMAIL>

            latestOfflineTime = <EMAIL>?.let { timeStr ->
                try {
                    LocalDateTimeUtil.parseNormal(timeStr)
                } catch (e: Exception) {
                    null
                }
            }

            latestOfflineUserName = <EMAIL>
            syncState = <EMAIL>
            errorMsg = <EMAIL>
            stockType = <EMAIL>
            stockTypeName = <EMAIL>
            delayDeliveryDays = <EMAIL>
            styleType = <EMAIL>
            imagePackageState = <EMAIL>
            marketCode = <EMAIL>
            marketSeriesCode = <EMAIL>
            clothingStyleCode = <EMAIL>
            planningType = <EMAIL>
            selectStyleName = <EMAIL>
            skuCount = <EMAIL>
            skcCount = <EMAIL>
            platformProductId = <EMAIL>

            // 将List<String>转换为逗号分隔的字符串
            skcList = <EMAIL>?.joinToString(",")
        }
    }
}
